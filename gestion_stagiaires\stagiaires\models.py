from datetime import date, timedelta
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.contrib import admin
from django.conf import settings  # Ajout de l'import manquant
import os


def convention_upload_path(instance, filename):
    """Génère le chemin d'upload pour les conventions de stage"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"convention_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"conventions/{safe_filename}"


def attestation_upload_path(instance, filename):
    """Génère le chemin d'upload pour les attestations de fin de stage"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"attestation_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"attestations/{safe_filename}"


def cv_upload_path(instance, filename):
    """Génère le chemin d'upload pour les CV"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"cv_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"cv/{safe_filename}"


def assurance_upload_path(instance, filename):
    """Génère le chemin d'upload pour les assurances"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"assurance_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"assurances/{safe_filename}"
from django.utils import timezone

# Définir les choix pour le niveau de difficulté
NIVEAU_DIFFICULTE_CHOICES = [
    ('FACILE', 'Facile'),
    ('MOYEN', 'Moyen'),
    ('DIFFICILE', 'Difficile'),
    ('EXPERT', 'Expert'),
]

class Service(models.Model):
    nom = models.CharField(max_length=100, verbose_name="Nom du service")
    code_service = models.CharField(max_length=10, unique=True, verbose_name="Code du service")
    description = models.TextField(blank=True, null=True, verbose_name="Description")
    responsable = models.ForeignKey('CustomUser', on_delete=models.SET_NULL, null=True, blank=True, related_name='services_responsable', verbose_name="Responsable")
    actif = models.BooleanField(default=True, verbose_name="Service actif")
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    cree_par = models.ForeignKey('CustomUser', on_delete=models.SET_NULL, null=True, blank=True, related_name='services_crees', verbose_name="Créé par")
    date_modification = models.DateTimeField(auto_now=True, null=True, verbose_name="Date de modification")
    modifie_par = models.ForeignKey('CustomUser', on_delete=models.SET_NULL, null=True, blank=True, related_name='services_modifies', verbose_name="Modifié par")
    
    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"
        ordering = ['nom']
    
    def __str__(self):
        return self.nom


class CustomUser(AbstractUser):
    date_creation = models.DateTimeField(default=timezone.now)
    ROLE_CHOICES = [
        ('ADMIN', 'Administrateur'),
        ('RH', 'Gestionnaire RH'),
        ('ENCADRANT', 'Encadrant'),
        ('STAGIAIRE', 'Stagiaire'),

    ]
    
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='STAGIAIRE', verbose_name="Rôle")
    service = models.ForeignKey(
        'Service',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='utilisateurs',
        verbose_name="Service"
    )
    
    def get_role_display(self):
        return dict(self.ROLE_CHOICES).get(self.role, self.role)

    class Meta:
        verbose_name = 'Utilisateur personnalisé'
        verbose_name_plural = 'Utilisateurs personnalisés'

    @property
    def is_admin(self):
        """Vérifie si l'utilisateur a des privilèges d'administration"""
        return self.is_superuser


class Thematique(models.Model):
    """Modèle pour les thématiques de stage"""
    titre = models.CharField(max_length=200, verbose_name="Titre")
    description = models.TextField(blank=True, null=True, verbose_name="Description")
    active = models.BooleanField(default=True, verbose_name="Active")
    service = models.ForeignKey(
        'Service',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='thematiques',
        verbose_name="Service"
    )
    cree_par = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='thematiques_creees',
        verbose_name="Créée par"
    )
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")
    
    class Meta:
        verbose_name = "Thématique"
        verbose_name_plural = "Thématiques"
        ordering = ['-date_creation']
        
    def __str__(self):
        return self.titre




class Sujet(models.Model):
    """Modèle pour les sujets de stage"""
    titre = models.CharField(max_length=200, verbose_name="Titre")
    description = models.TextField(default="", verbose_name="Description")  # Ajout d'une valeur par défaut
    thematique = models.ForeignKey(
        'Thematique',
        on_delete=models.CASCADE,
        related_name='sujets',
        verbose_name="Thématique"
    )
    service = models.ForeignKey(
        'Service',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sujets',
        verbose_name="Service"
    )
    encadrant = models.ForeignKey(
        'CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sujets_encadres',
        verbose_name="Encadrant"
    )
    duree_recommandee = models.PositiveIntegerField(
        default=30,
        verbose_name="Durée recommandée (jours)"
    )
    niveau_difficulte = models.CharField(
        max_length=20,
        choices=[
            ('FACILE', 'Facile'),
            ('MOYEN', 'Moyen'),
            ('DIFFICILE', 'Difficile')
        ],
        default='MOYEN',
        verbose_name="Niveau de difficulté"
    )
    competences_requises = models.TextField(
        blank=True,
        null=True,
        verbose_name="Compétences requises"
    )
    actif = models.BooleanField(default=True, verbose_name="Actif")
    cree_par = models.ForeignKey(
        'CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        related_name='sujets_crees',
        verbose_name="Créé par"
    )
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")
    
    class Meta:
        verbose_name = "Sujet"
        verbose_name_plural = "Sujets"
        ordering = ['-date_creation']



class Stagiaire(models.Model):
    """Modèle pour les stagiaires"""

    STATUT_CHOICES = [
        ('EN_COURS', 'En cours'),
        ('TERMINE', 'Terminé'),
        ('SUSPENDU', 'Suspendu'),
        ('ANNULE', 'Annulé'),
    ]

    STATUT_CONVENTION_CHOICES = [
        ('EN_ATTENTE', 'En attente'),
        ('VALIDEE', 'Validée'),
        ('REJETEE', 'Rejetée'),
        ('MODIFIEE', 'À modifier'),
    ]

    STATUT_TACHES_CHOICES = [
        ('NON_COMMENCEES', 'Non commencées'),
        ('EN_COURS', 'En cours'),
        ('PARTIELLEMENT_ACCOMPLIES', 'Partiellement accomplies'),
        ('ACCOMPLIES', 'Accomplies'),
    ]

    DEPARTEMENT_CHOICES = [
        ('IT', 'Informatique'),
        ('MARKETING', 'Marketing'),
        ('RH', 'Ressources Humaines'),
        ('FINANCE', 'Finance'),
        ('COMMERCIAL', 'Commercial'),
        ('PRODUCTION', 'Production'),
    ]

    # Informations personnelles
    nom = models.CharField(max_length=100, verbose_name="Nom")
    prenom = models.CharField(max_length=100, verbose_name="Prénom")
    email = models.EmailField(unique=True, verbose_name="Email")
    telephone = models.CharField(max_length=20, blank=True, verbose_name="Téléphone")
    date_naissance = models.DateField(verbose_name="Date de naissance")

    # Informations du stage
    departement = models.CharField(max_length=20, choices=DEPARTEMENT_CHOICES, verbose_name="Département")
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='stagiaires_service', verbose_name="Service")
    encadrant = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                 limit_choices_to={'role': 'ENCADRANT'}, verbose_name="Encadrant")
    date_debut = models.DateField(verbose_name="Date de début")
    date_fin = models.DateField(verbose_name="Date de fin")
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_COURS', verbose_name="Statut")

    # Informations académiques
    etablissement = models.CharField(max_length=200, verbose_name="Établissement")
    niveau_etude = models.CharField(max_length=100, verbose_name="Niveau d'étude")
    specialite = models.CharField(max_length=100, verbose_name="Spécialité")

    # Gestion des documents
    cv = models.FileField(upload_to=cv_upload_path, blank=True, null=True,
                         verbose_name="CV du stagiaire")
    assurance = models.FileField(upload_to=assurance_upload_path, blank=True, null=True,
                               verbose_name="Assurance responsabilité civile")

    # Gestion des conventions de stage
    convention_stage = models.FileField(upload_to=convention_upload_path, blank=True, null=True,
                                       verbose_name="Convention de stage")
    statut_convention = models.CharField(max_length=20, choices=STATUT_CONVENTION_CHOICES,
                                       default='EN_ATTENTE', verbose_name="Statut de la convention")
    date_validation_convention = models.DateTimeField(blank=True, null=True,
                                                     verbose_name="Date de validation de la convention")
    validee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='conventions_validees', verbose_name="Validée par")
    commentaire_convention = models.TextField(blank=True, verbose_name="Commentaire sur la convention")

    # Gestion des tâches et attestations
    description_taches = models.TextField(blank=True, verbose_name="Description des tâches à accomplir")
    statut_taches = models.CharField(max_length=30, choices=STATUT_TACHES_CHOICES,
                                   default='NON_COMMENCEES', verbose_name="Statut des tâches")
    evaluation_encadrant = models.TextField(blank=True, verbose_name="Évaluation de l'encadrant")
    note_finale = models.DecimalField(max_digits=4, decimal_places=2, blank=True, null=True,
                                    verbose_name="Note finale (/20)")

    # Attestation de fin de stage
    attestation_fin_stage = models.FileField(upload_to=attestation_upload_path, blank=True, null=True,
                                           verbose_name="Attestation de fin de stage")
    date_generation_attestation = models.DateTimeField(blank=True, null=True,
                                                      verbose_name="Date de génération de l'attestation")
    attestation_generee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                               related_name='attestations_generees',
                                               verbose_name="Attestation générée par")

    # Métadonnées
    date_creation = models.DateTimeField(default=timezone.now, verbose_name="Date de création")
    cree_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True,
                                related_name='stagiaires_crees', verbose_name="Créé par")

    # Nouveaux champs pour les thématiques et sujets
    thematique = models.ForeignKey(Thematique, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='stagiaires', verbose_name="Thématique")
    sujet = models.ForeignKey(Sujet, on_delete=models.SET_NULL, null=True, blank=True,
                             related_name='stagiaires', verbose_name="Sujet")
    duree_estimee = models.PositiveIntegerField(
        default=0, 
        verbose_name="Durée estimée (jours)",
        help_text="Durée estimée du stage en jours"
    )

    # Ajout du champ technologies
    technologies = models.TextField(blank=True, null=True, 
                                   verbose_name="Technologies utilisées",
                                   help_text="Technologies, langages ou outils que le stagiaire utilisera")

    class Meta:
        verbose_name = 'Stagiaire'
        verbose_name_plural = 'Stagiaires'
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.prenom} {self.nom} - {self.departement}"

    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"

    @property
    def duree_stage(self):
        """Calcule la durée du stage en jours"""
        if self.date_debut and self.date_fin:
            return (self.date_fin - self.date_debut).days + 1
        return 0

    @property
    def stage_termine(self):
        """Vérifie si le stage est terminé (date de fin dépassée)"""
        from datetime import date
        if not self.date_fin:
            return False
        return date.today() > self.date_fin

    @property
    def peut_generer_attestation(self):
        """Vérifie si l'attestation peut être générée"""
        return (self.stage_termine and
                self.statut_taches == 'ACCOMPLIES' and
                self.statut_convention == 'VALIDEE')

    @property
    def convention_validee(self):
        """Vérifie si la convention est validée"""
        return self.statut_convention == 'VALIDEE'

    @property
    def taches_accomplies(self):
        """Vérifie si toutes les tâches sont accomplies"""
        return self.statut_taches == 'ACCOMPLIES'

    def peut_valider_convention(self, user):
        """Vérifie si l'utilisateur peut valider la convention"""
        return user.role in ['RH', 'ADMIN'] and self.convention_stage

    def peut_generer_attestation_user(self, user):
        """Vérifie si l'utilisateur peut générer l'attestation"""
        return user.role in ['RH', 'ADMIN'] and self.peut_generer_attestation

    def get_progress_info(self):
        """
        Calcule les informations de progression du stage
        """
        today = date.today()
        total_days = (self.date_fin - self.date_debut).days + 1
        
        if today < self.date_debut:
            # Stage pas encore commencé
            days_until_start = (self.date_debut - today).days
            progress_percentage = 0
            days_elapsed = 0
        elif today > self.date_fin:
            # Stage terminé
            days_until_start = 0
            progress_percentage = 100
            days_elapsed = total_days
        else:
            # Stage en cours
            days_until_start = 0
            days_elapsed = (today - self.date_debut).days + 1
            progress_percentage = min(100, int((days_elapsed / total_days) * 100))
        
        return {
            'total_days': total_days,
            'days_elapsed': days_elapsed,
            'days_until_start': days_until_start,
            'progress_percentage': progress_percentage
        }

class Tache(models.Model):
    """Modèle pour les tâches assignées aux stagiaires"""
    
    STATUT_CHOICES = [
        ('A_FAIRE', 'À faire'),
        ('EN_COURS', 'En cours'),
        ('TERMINEE', 'Terminée'),
        ('ANNULEE', 'Annulée'),
    ]
    
    PRIORITE_CHOICES = [
        ('BASSE', 'Basse'),
        ('NORMALE', 'Normale'),
        ('HAUTE', 'Haute'),
        ('URGENTE', 'Urgente'),
    ]
    
    titre = models.CharField(max_length=200, verbose_name="Titre")
    description = models.TextField(blank=True, null=True, verbose_name="Description")
    stagiaire = models.ForeignKey(
        'Stagiaire', 
        on_delete=models.CASCADE, 
        related_name='taches',
        verbose_name="Stagiaire"
    )
    statut = models.CharField(
        max_length=20, 
        choices=STATUT_CHOICES, 
        default='A_FAIRE',
        verbose_name="Statut"
    )
    priorite = models.CharField(
        max_length=20, 
        choices=PRIORITE_CHOICES, 
        default='NORMALE',
        verbose_name="Priorité"
    )
    date_debut = models.DateField(null=True, blank=True, verbose_name="Date de début")
    date_fin_prevue = models.DateField(null=True, blank=True, verbose_name="Date de fin prévue")
    date_debut_reelle = models.DateField(null=True, blank=True, verbose_name="Date de début réelle")
    date_fin_reelle = models.DateField(null=True, blank=True, verbose_name="Date de fin réelle")
    
    creee_par = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='taches_creees',
        verbose_name="Créée par"
    )
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")
    
    class Meta:
        verbose_name = "Tâche"
        verbose_name_plural = "Tâches"
        ordering = ['-date_creation']
    
    def __str__(self):
        return self.titre
    
    def est_en_retard(self):
        """Vérifie si la tâche est en retard"""
        if self.statut in ['TERMINEE', 'ANNULEE']:
            return False
        if not self.date_fin_prevue:
            return False
        from django.utils import timezone
        return self.date_fin_prevue < timezone.now().date()
    
    def calculer_duree_prevue(self):
        """Calcule la durée prévue en jours"""
        if not self.date_debut or not self.date_fin_prevue:
            return None
        return (self.date_fin_prevue - self.date_debut).days
    
    def calculer_duree_reelle(self):
        """Calcule la durée réelle en jours"""
        if not self.date_debut_reelle or not self.date_fin_reelle:
            return None
        return (self.date_fin_reelle - self.date_debut_reelle).days
    
    def demarrer(self):
        """Démarre la tâche"""
        if self.statut == 'A_FAIRE':
            self.statut = 'EN_COURS'
            self.date_debut_reelle = timezone.now().date()
            self.save()
            return True
        return False
    
    def terminer(self):
        """Termine la tâche"""
        if self.statut in ['A_FAIRE', 'EN_COURS']:
            self.statut = 'TERMINEE'
            self.date_fin_reelle = timezone.now().date()
            self.save()
            return True
        return False
    
    def annuler(self):
        """Annule la tâche"""
        if self.statut != 'TERMINEE':
            self.statut = 'ANNULEE'
            self.save()
            return True
        return False


def rapport_upload_path(instance, filename):
    """Fonction pour définir le chemin d'upload des rapports"""
    return f'rapports/{instance.stagiaire.id}/{filename}'


class Mission(models.Model):
    """Modèle pour les missions assignées aux stagiaires"""

    STATUT_MISSION_CHOICES = [
        ('PLANIFIEE', 'Planifiée'),
        ('EN_COURS', 'En cours'),
        ('TERMINEE', 'Terminée'),
        ('VALIDEE', 'Validée'),
        ('REJETEE', 'Rejetée'),
    ]

    PRIORITE_CHOICES = [
        (1, 'Très haute'),
        (2, 'Haute'),
        (3, 'Moyenne'),
        (4, 'Basse'),
        (5, 'Très basse'),
    ]

    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE, related_name='missions')
    titre = models.CharField(max_length=200, verbose_name="Titre de la mission")
    description = models.TextField(verbose_name="Description détaillée")
    objectifs = models.TextField(verbose_name="Objectifs à atteindre")
    livrables_attendus = models.TextField(verbose_name="Livrables attendus")

    date_debut_prevue = models.DateField(verbose_name="Date de début prévue")
    date_fin_prevue = models.DateField(verbose_name="Date de fin prévue")
    date_debut_reelle = models.DateField(null=True, blank=True, verbose_name="Date de début réelle")
    date_fin_reelle = models.DateField(null=True, blank=True, verbose_name="Date de fin réelle")

    priorite = models.IntegerField(choices=PRIORITE_CHOICES, default=3, verbose_name="Priorité")
    statut = models.CharField(max_length=20, choices=STATUT_MISSION_CHOICES, default='PLANIFIEE', verbose_name="Statut")

    # Suivi d'avancement
    pourcentage_avancement = models.IntegerField(default=0, verbose_name="Pourcentage d'avancement")
    commentaire_avancement = models.TextField(blank=True, verbose_name="Commentaire sur l'avancement")
    derniere_mise_a_jour = models.DateTimeField(auto_now=True, verbose_name="Dernière mise à jour")

    # Métadonnées
    creee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='missions_creees')
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")

    class Meta:
        verbose_name = "Mission"
        verbose_name_plural = "Missions"
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.titre} - {self.stagiaire.nom_complet}"

    @property
    def duree_prevue(self):
        """Calcule la durée prévue en jours"""
        if self.date_debut_prevue and self.date_fin_prevue:
            return (self.date_fin_prevue - self.date_debut_prevue).days + 1
        return 0

    @property
    def duree_reelle(self):
        """Calcule la durée réelle en jours"""
        if self.date_debut_reelle and self.date_fin_reelle:
            return (self.date_fin_reelle - self.date_debut_reelle).days + 1
        return 0

    @property
    def en_retard(self):
        """Vérifie si la mission est en retard"""
        from django.utils import timezone
        return (self.date_fin_prevue < timezone.now().date() and
                self.statut not in ['TERMINEE', 'VALIDEE'])


class RapportStage(models.Model):
    """Modèle pour les rapports de stage"""

    STATUT_RAPPORT_CHOICES = [
        ('BROUILLON', 'Brouillon'),
        ('SOUMIS', 'Soumis'),
        ('EN_REVISION', 'En révision'),
        ('VALIDE', 'Validé'),
        ('REJETE', 'Rejeté'),
    ]

    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE, related_name='rapports')
    mission = models.ForeignKey(Mission, on_delete=models.CASCADE, related_name='rapports', null=True, blank=True)

    titre = models.CharField(max_length=200, verbose_name="Titre du rapport")
    fichier_rapport = models.FileField(upload_to=rapport_upload_path, verbose_name="Fichier du rapport")
    description = models.TextField(verbose_name="Description du contenu")

    statut = models.CharField(max_length=20, choices=STATUT_RAPPORT_CHOICES, default='BROUILLON', verbose_name="Statut")

    # Validation par l'encadrant
    date_soumission = models.DateTimeField(null=True, blank=True, verbose_name="Date de soumission")
    date_validation = models.DateTimeField(null=True, blank=True, verbose_name="Date de validation")
    valide_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, related_name='rapports_valides', null=True, blank=True)

    commentaires_encadrant = models.TextField(blank=True, verbose_name="Commentaires de l'encadrant")
    note_rapport = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True, verbose_name="Note du rapport")

    # Métadonnées
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")

    class Meta:
        verbose_name = "Rapport de stage"
        verbose_name_plural = "Rapports de stage"
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.titre} - {self.stagiaire.nom_complet}"

    def save(self, *args, **kwargs):
        # Mettre à jour la date de soumission si le statut change vers SOUMIS
        if self.statut == 'SOUMIS' and not self.date_soumission:
            from django.utils import timezone
            self.date_soumission = timezone.now()

        # Mettre à jour la date de validation si le statut change vers VALIDE
        if self.statut == 'VALIDE' and not self.date_validation:
            from django.utils import timezone
            self.date_validation = timezone.now()

        super().save(*args, **kwargs)


def contrat_upload_path(instance, filename):
    """Génère le chemin d'upload pour les contrats de stage"""
    return f'contrats/{instance.stagiaire.nom}_{instance.stagiaire.prenom}_{filename}'


class ContratStage(models.Model):
    """Modèle pour les contrats de stage créés par l'admin"""

    STATUT_CONTRAT_CHOICES = [
        ('BROUILLON', 'Brouillon'),
        ('EN_ATTENTE_SIGNATURE', 'En attente de signature'),
        ('PARTIELLEMENT_SIGNE', 'Partiellement signé'),
        ('ENTIEREMENT_SIGNE', 'Entièrement signé'),
        ('EXPIRE', 'Expiré'),
        ('ANNULE', 'Annulé'),
    ]

    TYPE_CONTRAT_CHOICES = [
        ('STAGE_OBLIGATOIRE', 'Stage obligatoire'),
        ('STAGE_VOLONTAIRE', 'Stage volontaire'),
        ('STAGE_DECOUVERTE', 'Stage de découverte'),
        ('STAGE_PERFECTIONNEMENT', 'Stage de perfectionnement'),
    ]

    # Informations de base
    reference = models.CharField(max_length=50, unique=True, verbose_name="Référence du contrat")
    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE,
                                 related_name='contrats', verbose_name="Stagiaire")
    type_contrat = models.CharField(max_length=30, choices=TYPE_CONTRAT_CHOICES,
                                   default='STAGE_OBLIGATOIRE', verbose_name="Type de contrat")

    # Contenu du contrat
    titre_stage = models.CharField(max_length=200, verbose_name="Titre du stage")
    description_missions = models.TextField(verbose_name="Description des missions")
    objectifs_pedagogiques = models.TextField(verbose_name="Objectifs pédagogiques")
    competences_acquises = models.TextField(blank=True, verbose_name="Compétences à acquérir")

    # Conditions du stage
    duree_hebdomadaire = models.IntegerField(default=35, verbose_name="Durée hebdomadaire (heures)")
    gratification_mensuelle = models.DecimalField(max_digits=8, decimal_places=2,
                                                 blank=True, null=True, verbose_name="Gratification mensuelle (€)")
    avantages = models.TextField(blank=True, verbose_name="Avantages (tickets restaurant, etc.)")

    # Encadrement
    encadrant_entreprise = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True,
                                           limit_choices_to={'role': 'ENCADRANT'},
                                           related_name='contrats_encadres',
                                           verbose_name="Encadrant entreprise")
    tuteur_pedagogique = models.CharField(max_length=200, blank=True,
                                        verbose_name="Tuteur pédagogique (établissement)")

    # Statut et signatures
    statut = models.CharField(max_length=30, choices=STATUT_CONTRAT_CHOICES,
                             default='BROUILLON', verbose_name="Statut")

    # Signature électronique (RH uniquement)
    signature_rh = models.BooleanField(default=False, verbose_name="Signé par RH")
    date_signature_rh = models.DateTimeField(blank=True, null=True,
                                           verbose_name="Date signature RH")
    signature_rh_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                       related_name='contrats_signes_rh',
                                       verbose_name="Signé RH par")

    # Documents
    document_contrat = models.FileField(upload_to=contrat_upload_path, blank=True, null=True,
                                       verbose_name="Document du contrat")
    document_signe = models.FileField(upload_to=contrat_upload_path, blank=True, null=True,
                                     verbose_name="Document signé")

    # Métadonnées
    date_creation = models.DateTimeField(default=timezone.now, verbose_name="Date de création")
    cree_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True,
                                related_name='contrats_crees', verbose_name="Créé par")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Dernière modification")
    date_expiration = models.DateField(blank=True, null=True, verbose_name="Date d'expiration")

    # Commentaires et notes
    commentaires_admin = models.TextField(blank=True, verbose_name="Commentaires administrateur")
    notes_internes = models.TextField(blank=True, verbose_name="Notes internes")

    class Meta:
        verbose_name = 'Contrat de stage'
        verbose_name_plural = 'Contrats de stage'
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.reference} - {self.stagiaire.nom_complet}"

    def save(self, *args, **kwargs):
        # Générer automatiquement une référence si elle n'existe pas
        if not self.reference:
            from datetime import datetime
            year = datetime.now().year
            # Compter les contrats existants pour cette année
            count = self.__class__.objects.filter(
                reference__startswith=f'CTR-{year}'
            ).count() + 1
            self.reference = f'CTR-{year}-{count:03d}'

        # Mettre à jour le statut selon la signature RH
        if self.signature_rh:
            self.statut = 'ENTIEREMENT_SIGNE'
        elif self.statut == 'BROUILLON':
            pass  # Garder le statut brouillon
        else:
            self.statut = 'EN_ATTENTE_SIGNATURE'

        super().save(*args, **kwargs)

    @property
    def pourcentage_signatures(self):
        """Calcule le pourcentage de signatures complétées (RH uniquement)"""
        return 100 if self.signature_rh else 0

    @property
    def signatures_manquantes(self):
        """Retourne la liste des signatures manquantes"""
        return [] if self.signature_rh else ['RH']

    @property
    def est_expire(self):
        """Vérifie si le contrat est expiré"""
        if self.date_expiration:
            from django.utils import timezone
            return timezone.now().date() > self.date_expiration
        return False


class DureeEstimee(models.Model):
    """Modèle pour stocker les durées estimées des stages"""
    duree = models.IntegerField(verbose_name="Durée en jours")
    commentaire = models.TextField(blank=True, null=True)
    cree_par = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='durees_estimees_creees'
    )
    date_creation = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Durée estimée"
        verbose_name_plural = "Durées estimées"
        ordering = ['-date_creation']
    
    def __str__(self):
        return f"{self.duree} jours"
    
    @property
    def duree_en_mois(self):
        """Convertit la durée en jours en mois (approximatif)"""
        return round(self.duree / 30, 1)


# Create your models here.

# À la fin du fichier models.py, après avoir défini la classe Tache
# Créer un alias pour compatibilité
TacheStage = Tache
