{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un Stagiaire - Gestion des Stagiaires{% endblock %}

{% block extra_css %}
<style>
    .tech-suggestions .badge:hover {
        background-color: #e9ecef !important;
    }
    
    .tech-badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 0.85em;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
        background-color: #f8f9fa;
        color: #212529;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Ajouter un Nouveau Stagiaire
                    </h3>
                    <div>
                        <a href="{% url 'stagiaires_list' %}" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-list me-1"></i>Liste des stagiaires
                        </a>
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Tableau de bord
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="stagiaireForm" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <div class="row">
                            <!-- Colonne gauche -->
                            <div class="col-md-6">
                                <!-- Informations personnelles -->
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            Informations personnelles
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="{{ form.prenom.id_for_label }}" class="form-label">
                                                <i class="fas fa-user me-1"></i>Prénom *
                                            </label>
                                            {{ form.prenom }}
                                            {% if form.prenom.errors %}
                                                <div class="text-danger small mt-1">{{ form.prenom.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.nom.id_for_label }}" class="form-label">
                                                <i class="fas fa-user me-1"></i>Nom *
                                            </label>
                                            {{ form.nom }}
                                            {% if form.nom.errors %}
                                                <div class="text-danger small mt-1">{{ form.nom.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                                <i class="fas fa-envelope me-1"></i>Email *
                                            </label>
                                            {{ form.email }}
                                            {% if form.email.errors %}
                                                <div class="text-danger small mt-1">{{ form.email.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.telephone.id_for_label }}" class="form-label">
                                                <i class="fas fa-phone me-1"></i>Téléphone
                                            </label>
                                            {{ form.telephone }}
                                            {% if form.telephone.errors %}
                                                <div class="text-danger small mt-1">{{ form.telephone.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.date_naissance.id_for_label }}" class="form-label">
                                                <i class="fas fa-birthday-cake me-1"></i>Date de naissance *
                                            </label>
                                            {{ form.date_naissance }}
                                            {% if form.date_naissance.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_naissance.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Informations académiques -->
                                <div class="card mb-4">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">
                                            <i class="fas fa-graduation-cap me-2"></i>
                                            Informations académiques
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="{{ form.etablissement.id_for_label }}" class="form-label">
                                                <i class="fas fa-university me-1"></i>Établissement *
                                            </label>
                                            {{ form.etablissement }}
                                            {% if form.etablissement.errors %}
                                                <div class="text-danger small mt-1">{{ form.etablissement.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.niveau_etude.id_for_label }}" class="form-label">
                                                <i class="fas fa-layer-group me-1"></i>Niveau d'étude *
                                            </label>
                                            {{ form.niveau_etude }}
                                            {% if form.niveau_etude.errors %}
                                                <div class="text-danger small mt-1">{{ form.niveau_etude.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.specialite.id_for_label }}" class="form-label">
                                                <i class="fas fa-star me-1"></i>Spécialité *
                                            </label>
                                            {{ form.specialite }}
                                            {% if form.specialite.errors %}
                                                <div class="text-danger small mt-1">{{ form.specialite.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Colonne droite -->
                            <div class="col-md-6">
                                <!-- Informations du stage -->
                                <div class="card mb-4">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-briefcase me-2"></i>
                                            Informations du stage
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="{{ form.departement.id_for_label }}" class="form-label">
                                                <i class="fas fa-building me-1"></i>Département *
                                            </label>
                                            {{ form.departement }}
                                            {% if form.departement.errors %}
                                                <div class="text-danger small mt-1">{{ form.departement.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                       
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.date_debut.id_for_label }}" class="form-label">
                                                <i class="fas fa-calendar-alt me-1"></i>Date de début *
                                            </label>
                                            {{ form.date_debut }}
                                            {% if form.date_debut.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_debut.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.date_fin.id_for_label }}" class="form-label">
                                                <i class="fas fa-calendar-check me-1"></i>Date de fin *
                                            </label>
                                            {{ form.date_fin }}
                                            {% if form.date_fin.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_fin.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- Nouveau champ pour la thématique -->
                                        {% if form.thematique %}
                                        <div class="mb-3">
                                            <label for="{{ form.thematique.id_for_label }}" class="form-label">
                                                <i class="fas fa-tags me-1"></i>Thématique
                                            </label>
                                            {{ form.thematique }}
                                            {% if form.thematique.errors %}
                                                <div class="text-danger small mt-1">{{ form.thematique.errors }}</div>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        
                                        <!-- Nouveau champ pour le sujet -->
                                        {% if form.sujet %}
                                        <div class="mb-3">
                                            <label for="{{ form.sujet.id_for_label }}" class="form-label">
                                                <i class="fas fa-project-diagram me-1"></i>Sujet
                                            </label>
                                            {{ form.sujet }}
                                            {% if form.sujet.errors %}
                                                <div class="text-danger small mt-1">{{ form.sujet.errors }}</div>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        
                                        <!-- Nouveau champ pour le sujet -->
                                        {% if form.encadrant %}
                                        <div class="mb-3">
                                            <label for="{{ form.encadrant.id_for_label }}" class="form-label">
                                                <i class="fas fa-user-tie me-1"></i>Encadrant *
                                            </label>
                                            {{ form.encadrant }}
                                            {% if form.encadrant.errors %}
                                                <div class="text-danger small mt-1">{{ form.encadrant.errors }}</div>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                
                                <!-- Documents (Accessible uniquement aux RH) -->
                                {% if is_rh %}
                                <div class="card mb-4">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-file-alt me-2"></i>
                                            Documents du stagiaire
                                            <span class="badge bg-warning text-dark ms-2">
                                                <i class="fas fa-user-shield me-1"></i>RH uniquement
                                            </span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Documents requis :</strong> Téléchargez les documents nécessaires pour le dossier du stagiaire.
                                            Les formats acceptés sont : PDF, DOC, DOCX (et JPG, PNG pour l'assurance).
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.cv.id_for_label }}" class="form-label">
                                                <i class="fas fa-file-user me-1"></i>CV du stagiaire
                                            </label>
                                            {{ form.cv }}
                                            {% if form.cv.errors %}
                                                <div class="text-danger small mt-1">{{ form.cv.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-file-pdf me-1"></i>Formats acceptés : PDF, DOC, DOCX
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.assurance.id_for_label }}" class="form-label">
                                                <i class="fas fa-shield-alt me-1"></i>Assurance responsabilité civile
                                            </label>
                                            {{ form.assurance }}
                                            {% if form.assurance.errors %}
                                                <div class="text-danger small mt-1">{{ form.assurance.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-file-image me-1"></i>Formats acceptés : PDF, DOC, DOCX, JPG, PNG
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.convention_stage.id_for_label }}" class="form-label">
                                                <i class="fas fa-file-contract me-1"></i>Convention de stage
                                            </label>
                                            {{ form.convention_stage }}
                                            {% if form.convention_stage.errors %}
                                                <div class="text-danger small mt-1">{{ form.convention_stage.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-file-pdf me-1"></i>Formats acceptés : PDF, DOC, DOCX
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <!-- Message informatif pour les utilisateurs non-RH -->
                                <div class="alert alert-info mt-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Documents du stagiaire :</strong> L'ajout de documents (CV, assurance, convention)
                                    est réservé aux gestionnaires RH. Ces documents pourront être ajoutés ultérieurement
                                    par un utilisateur RH.
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>Enregistrer le stagiaire
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation des dates
    const dateDebut = document.getElementById('id_date_debut');
    const dateFin = document.getElementById('id_date_fin');

    if (dateDebut && dateFin) {
        dateDebut.addEventListener('change', function() {
            dateFin.min = this.value;
        });

        dateFin.addEventListener('change', function() {
            if (this.value < dateDebut.value) {
                alert('La date de fin ne peut pas être antérieure à la date de début.');
                this.value = '';
            }
        });
    }

    // Gestion des fichiers (seulement si les champs existent - pour les RH)
    const fileInputs = ['id_cv', 'id_assurance', 'id_convention_stage'];

    fileInputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Vérifier la taille du fichier (max 10MB)
                    const maxSize = 10 * 1024 * 1024; // 10MB
                    if (file.size > maxSize) {
                        alert('Le fichier est trop volumineux. Taille maximale : 10MB');
                        this.value = '';
                        return;
                    }

                    // Afficher le nom du fichier sélectionné
                    const label = this.parentElement.querySelector('label');
                    const fileName = file.name;
                    const originalText = label.textContent;

                    // Créer un indicateur de fichier sélectionné
                    let indicator = this.parentElement.querySelector('.file-indicator');
                    if (!indicator) {
                        indicator = document.createElement('div');
                        indicator.className = 'file-indicator mt-1';
                        this.parentElement.appendChild(indicator);
                    }

                    indicator.innerHTML = `
                        <small class="text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            Fichier sélectionné : <strong>${fileName}</strong>
                        </small>
                    `;
                }
            });
        }
    });

    // Validation du formulaire avant soumission
    const form = document.getElementById('stagiaireForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Vérifier que les champs obligatoires sont remplis
            const requiredFields = ['id_nom', 'id_prenom', 'id_email', 'id_date_naissance',
                                  'id_departement', 'id_date_debut', 'id_date_fin',
                                  'id_etablissement', 'id_niveau_etude', 'id_specialite'];

            let hasErrors = false;
            requiredFields.forEach(function(fieldId) {
                const field = document.getElementById(fieldId);
                if (field && !field.value.trim()) {
                    field.classList.add('is-invalid');
                    hasErrors = true;
                } else if (field) {
                    field.classList.remove('is-invalid');
                }
            });

            if (hasErrors) {
                e.preventDefault();
                alert('Veuillez remplir tous les champs obligatoires marqués d\'un astérisque (*).');
                return false;
            }

            // Afficher un message de chargement
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Enregistrement en cours...';
                submitBtn.disabled = true;
            }
        });
    }

    // Fonction pour filtrer les sujets en fonction de la thématique sélectionnée
    function updateSujets() {
        const thematiqueSelect = document.getElementById('id_thematique');
        const sujetSelect = document.getElementById('id_sujet');
        
        if (thematiqueSelect && sujetSelect) {
            const thematiqueId = thematiqueSelect.value;
            
            // Désactiver temporairement le select des sujets
            sujetSelect.disabled = true;
            
            // Sauvegarder la valeur actuelle
            const currentValue = sujetSelect.value;
            
            // Cacher tous les sujets
            Array.from(sujetSelect.options).forEach(option => {
                if (option.value) {  // Ignorer l'option vide
                    option.style.display = 'none';
                }
            });
            
            // Si une thématique est sélectionnée, afficher seulement les sujets correspondants
            if (thematiqueId) {
                // Faire une requête AJAX pour obtenir les sujets de cette thématique
                fetch(`/api/thematiques/${thematiqueId}/sujets/`)
                    .then(response => response.json())
                    .then(data => {
                        // Vider le select des sujets sauf l'option vide
                        while (sujetSelect.options.length > 1) {
                            sujetSelect.remove(1);
                        }
                        
                        // Ajouter les nouveaux sujets
                        data.forEach(sujet => {
                            const option = new Option(sujet.titre, sujet.id);
                            sujetSelect.add(option);
                        });
                        
                        // Réactiver le select
                        sujetSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Erreur lors de la récupération des sujets:', error);
                        sujetSelect.disabled = false;
                    });
            } else {
                // Si aucune thématique n'est sélectionnée, afficher tous les sujets
                Array.from(sujetSelect.options).forEach(option => {
                    option.style.display = '';
                });
                sujetSelect.disabled = false;
            }
        }
    }
    
    // Attacher l'événement au changement de thématique
    const thematiqueSelect = document.getElementById('id_thematique');
    if (thematiqueSelect) {
        thematiqueSelect.addEventListener('change', updateSujets);
    }
    
    // Exécuter une fois au chargement de la page
    updateSujets();
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fonctionnalité existante...
    
    // Ajout de la fonctionnalité d'auto-complétion pour les technologies
    const techField = document.getElementById('id_technologies');
    if (techField) {
        // Liste des technologies courantes
        const commonTechs = [
            'Python', 'Django', 'Flask', 'JavaScript', 'TypeScript', 'React', 'Angular', 'Vue.js',
            'Node.js', 'Express', 'PHP', 'Laravel', 'Symfony', 'Java', 'Spring', 'C#', '.NET',
            'HTML', 'CSS', 'SASS', 'Bootstrap', 'Tailwind CSS', 'SQL', 'MySQL', 'PostgreSQL',
            'MongoDB', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'Google Cloud',
            'Git', 'GitHub', 'GitLab', 'CI/CD', 'Jenkins', 'Ansible', 'Terraform',
            'Data Science', 'Machine Learning', 'TensorFlow', 'PyTorch', 'R', 'Tableau',
            'Power BI', 'Excel', 'Word', 'PowerPoint', 'Photoshop', 'Illustrator', 'Figma'
        ];
        
        // Créer un élément de suggestions
        const suggestionsDiv = document.createElement('div');
        suggestionsDiv.className = 'tech-suggestions mt-2 d-none';
        suggestionsDiv.style.maxHeight = '150px';
        suggestionsDiv.style.overflowY = 'auto';
        suggestionsDiv.style.border = '1px solid #ced4da';
        suggestionsDiv.style.borderRadius = '0.25rem';
        suggestionsDiv.style.padding = '0.5rem';
        techField.parentNode.appendChild(suggestionsDiv);
        
        // Fonction pour mettre à jour les suggestions
        function updateSuggestions() {
            const input = techField.value.toLowerCase();
            if (input.length < 2) {
                suggestionsDiv.classList.add('d-none');
                return;
            }
            
            // Filtrer les technologies qui correspondent à l'entrée
            const matches = commonTechs.filter(tech => 
                tech.toLowerCase().includes(input) && 
                !techField.value.includes(tech)
            );
            
            if (matches.length === 0) {
                suggestionsDiv.classList.add('d-none');
                return;
            }
            
            // Afficher les suggestions
            suggestionsDiv.innerHTML = '';
            suggestionsDiv.classList.remove('d-none');
            
            matches.slice(0, 10).forEach(tech => {
                const badge = document.createElement('span');
                badge.className = 'badge bg-light text-dark me-2 mb-2 p-2';
                badge.style.cursor = 'pointer';
                badge.textContent = tech;
                badge.addEventListener('click', function() {
                    // Ajouter la technologie au champ
                    const currentValue = techField.value.trim();
                    if (currentValue) {
                        techField.value = currentValue + ', ' + tech;
                    } else {
                        techField.value = tech;
                    }
                    suggestionsDiv.classList.add('d-none');
                    techField.focus();
                });
                suggestionsDiv.appendChild(badge);
            });
        }
        
        // Attacher les événements
        techField.addEventListener('input', updateSuggestions);
        techField.addEventListener('focus', updateSuggestions);
        
        // Fermer les suggestions en cliquant ailleurs
        document.addEventListener('click', function(e) {
            if (e.target !== techField && !suggestionsDiv.contains(e.target)) {
                suggestionsDiv.classList.add('d-none');
            }
        });
    }
});
</script>
{% endblock %}








