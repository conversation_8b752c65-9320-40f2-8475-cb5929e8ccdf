#!/usr/bin/env python
"""
Test du calendrier amélioré avec couleurs distinctes
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire
from django.test import Client
from datetime import date

User = get_user_model()

def test_calendrier_ameliore():
    """Test du calendrier amélioré"""
    
    print("=== Test du calendrier amélioré ===")
    
    # 1. Vérifier les données
    print("\n1️⃣ Vérification des données:")
    
    stagiaires = Stagiaire.objects.all()
    print(f"   Nombre de stagiaires: {stagiaires.count()}")
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"   Nombre d'encadrants: {encadrants.count()}")
    
    if not encadrants.exists():
        print("   ❌ Aucun encadrant trouvé pour le test")
        return
    
    # 2. Test avec un encadrant
    encadrant_test = encadrants.first()
    print(f"\n2️⃣ Test avec l'encadrant: {encadrant_test.get_full_name()}")
    print(f"   Service: {encadrant_test.service.nom if encadrant_test.service else 'Aucun'}")
    
    # 3. Test d'accès au calendrier
    print(f"\n3️⃣ Test d'accès au calendrier:")
    
    client = Client()
    client.force_login(encadrant_test)
    
    # Test avec différents paramètres
    test_urls = [
        '/calendrier/',
        '/calendrier/?vue=tous',
        '/calendrier/?vue=encadrant',
        f'/calendrier/?mois={date.today().month}&annee={date.today().year}',
    ]
    
    for url in test_urls:
        try:
            response = client.get(url)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {url} - Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Vérifier la présence d'éléments clés
                checks = [
                    ('calendrier-ameliore', 'Calendrier amélioré'),
                    ('stagiaire-color-box', 'Boîtes de couleur'),
                    ('calendrier-table', 'Table du calendrier'),
                    ('stagiaire-badge', 'Badges des stagiaires'),
                ]
                
                for check_class, description in checks:
                    if check_class in content:
                        print(f"      ✅ {description} présent")
                    else:
                        print(f"      ⚠️ {description} manquant")
        
        except Exception as e:
            print(f"   ❌ {url} - Erreur: {e}")
    
    # 4. Test du filtrage par service
    print(f"\n4️⃣ Test du filtrage par service:")
    
    from stagiaires.views import filter_stagiaires_by_user_role
    
    stagiaires_filtres = filter_stagiaires_by_user_role(encadrant_test)
    print(f"   Stagiaires visibles par l'encadrant: {stagiaires_filtres.count()}")
    
    for stagiaire in stagiaires_filtres:
        print(f"      • {stagiaire.nom_complet} - {stagiaire.get_departement_display()}")
    
    # 5. Test de génération des couleurs
    print(f"\n5️⃣ Test de génération des couleurs:")
    
    couleurs_disponibles = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', 
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
        '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
    ]
    
    couleurs_stagiaires = {}
    for i, stagiaire in enumerate(stagiaires_filtres):
        couleur_index = i % len(couleurs_disponibles)
        couleurs_stagiaires[stagiaire.id] = couleurs_disponibles[couleur_index]
        print(f"   • {stagiaire.nom_complet}: {couleurs_disponibles[couleur_index]}")
    
    # 6. Test du calendrier mensuel
    print(f"\n6️⃣ Test du calendrier mensuel:")
    
    import calendar
    from datetime import date
    
    mois_actuel = date.today().month
    annee_actuelle = date.today().year
    
    cal = calendar.monthcalendar(annee_actuelle, mois_actuel)
    print(f"   Calendrier pour {mois_actuel}/{annee_actuelle}:")
    print(f"   Nombre de semaines: {len(cal)}")
    
    # Compter les stagiaires par jour
    jours_avec_stagiaires = 0
    total_stagiaires_mois = 0
    
    for semaine in cal:
        for jour in semaine:
            if jour != 0:
                date_jour = date(annee_actuelle, mois_actuel, jour)
                stagiaires_du_jour = 0
                
                for stagiaire in stagiaires_filtres:
                    if stagiaire.date_debut <= date_jour <= stagiaire.date_fin:
                        stagiaires_du_jour += 1
                
                if stagiaires_du_jour > 0:
                    jours_avec_stagiaires += 1
                    total_stagiaires_mois += stagiaires_du_jour
    
    print(f"   Jours avec des stagiaires: {jours_avec_stagiaires}")
    print(f"   Total stagiaires-jours: {total_stagiaires_mois}")
    
    # 7. Recommandations
    print(f"\n7️⃣ Fonctionnalités du calendrier amélioré:")
    print(f"   ✅ Couleurs distinctes pour chaque stagiaire")
    print(f"   ✅ Légende avec nom et service de chaque stagiaire")
    print(f"   ✅ Vue calendrier mensuel avec badges colorés")
    print(f"   ✅ Tooltips avec informations détaillées")
    print(f"   ✅ Navigation entre les mois")
    print(f"   ✅ Liste détaillée avec barres de progression colorées")
    print(f"   ✅ Responsive design pour mobile")
    print(f"   ✅ Filtrage par service pour les encadrants")
    
    print(f"\n🎯 Pour tester le calendrier:")
    print(f"   1. Connectez-vous en tant qu'encadrant")
    print(f"   2. Allez sur /calendrier/")
    print(f"   3. Chaque stagiaire aura une couleur unique")
    print(f"   4. Survolez les badges pour voir les détails")
    print(f"   5. Utilisez les boutons pour naviguer entre les mois")

if __name__ == '__main__':
    test_calendrier_ameliore()
