{% extends 'stagiaires/base.html' %}

{% block title %}Modifier le Service - {{ service.nom }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Modifier le service : {{ service.nom }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.nom.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Nom du service *
                                    </label>
                                    {{ form.nom }}
                                    {% if form.nom.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.nom.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.code_service.id_for_label }}" class="form-label">
                                        <i class="fas fa-code me-1"></i>Code du service *
                                    </label>
                                    {{ form.code_service }}
                                    <div class="form-text">Code unique (ex: IT, RH, COMPTA)</div>
                                    {% if form.code_service.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.code_service.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description
                            </label>
                            {{ form.description }}
                            <div class="form-text">Description détaillée du service et de ses missions</div>
                            {% if form.description.errors %}
                                <div class="text-danger small">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.responsable.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>Responsable du service
                                    </label>
                                    {{ form.responsable }}
                                    <div class="form-text">Utilisateur responsable de ce service</div>
                                    {% if form.responsable.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.responsable.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.actif.id_for_label }}" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>Statut
                                    </label>
                                    <div class="form-check">
                                        {{ form.actif }}
                                        <label class="form-check-label" for="{{ form.actif.id_for_label }}">
                                            Service actif
                                        </label>
                                    </div>
                                    <div class="form-text">Un service inactif n'apparaîtra pas dans les formulaires</div>
                                    {% if form.actif.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.actif.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'services_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Informations sur le service -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations sur le service</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Date de création :</strong> {{ service.date_creation|date:"d/m/Y à H:i" }}</p>
                            {% if service.cree_par %}
                                <p><strong>Créé par :</strong> {{ service.cree_par.get_full_name }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <p><strong>Nombre de stagiaires :</strong> 
                                <span class="badge bg-info">{{ service.nombre_stagiaires }} total</span>
                                {% if service.stagiaires_actifs > 0 %}
                                    <span class="badge bg-success">{{ service.stagiaires_actifs }} actifs</span>
                                {% endif %}
                            </p>
                            <p><strong>Statut actuel :</strong> 
                                {% if service.actif %}
                                    <span class="badge bg-success">Actif</span>
                                {% else %}
                                    <span class="badge bg-warning">Inactif</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    {% if service.nombre_stagiaires > 0 %}
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Attention :</strong> Ce service a {{ service.nombre_stagiaires }} stagiaire(s) associé(s). 
                            Si vous désactivez ce service, il n'apparaîtra plus dans les formulaires d'ajout de nouveaux stagiaires, 
                            mais les stagiaires existants conserveront leur affectation.

                            {% extends 'base.html' %}

{% block content %}
<h2>Modifier le service</h2>
<form method="post">
    {% csrf_token %}
    {{ form.as_p }}
    <button type="submit" class="btn btn-primary">Enregistrer</button>
    <a href="{% url 'services_list' %}" class="btn btn-secondary">Annuler</a>
</form>
{% endblock %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
