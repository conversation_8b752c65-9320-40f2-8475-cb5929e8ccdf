{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- En-tête avec navigation -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Calendrier des Stages
                            </h3>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="btn-group" role="group">
                                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-light">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <span class="btn btn-light disabled">
                                    {{ month_name }} {{ year }}
                                </span>
                                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-light">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light">
                                <i class="fas fa-list me-1"></i>
                                Liste des Stagiaires
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Légende des couleurs -->
            {% if stagiaires_data %}
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Légende des Stagiaires
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for data in stagiaires_data %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-0" style="background: linear-gradient(135deg, {{ data.color }}20, {{ data.color }}10); border-left: 4px solid {{ data.color }} !important;">
                                <div class="card-body p-2">
                                    <div class="d-flex align-items-center mb-1">
                                        <div class="me-2" style="width: 16px; height: 16px; background: linear-gradient(135deg, {{ data.color }}, {{ data.color }}dd); border-radius: 50%; box-shadow: 0 1px 3px rgba(0,0,0,0.2);"></div>
                                        <span class="fw-bold text-dark" style="font-size: 0.9rem;">{{ data.stagiaire.nom_complet }}</span>
                                    </div>
                                    <div class="ms-3">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ data.stagiaire.date_debut|date:"d/m" }} - {{ data.stagiaire.date_fin|date:"d/m/Y" }}
                                            <span class="badge bg-secondary ms-1" style="font-size: 0.6rem;">{{ data.duree_semaines }}sem</span>
                                        </small>
                                        {% if data.stagiaire.sujet %}
                                        <small class="text-info d-block">
                                            <i class="fas fa-lightbulb me-1"></i>
                                            {{ data.stagiaire.sujet.titre|truncatechars:35 }}
                                        </small>
                                        {% endif %}
                                        {% if data.stagiaire.service %}
                                        <small class="text-success d-block">
                                            <i class="fas fa-building me-1"></i>
                                            {{ data.stagiaire.service.nom }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Calendrier principal -->
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0" style="table-layout: fixed;">
                            <!-- En-tête des jours de la semaine -->
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 120px;" class="text-center">Stagiaires</th>
                                    <th class="text-center">Lundi</th>
                                    <th class="text-center">Mardi</th>
                                    <th class="text-center">Mercredi</th>
                                    <th class="text-center">Jeudi</th>
                                    <th class="text-center">Vendredi</th>
                                    <th class="text-center text-muted">Samedi</th>
                                    <th class="text-center text-muted">Dimanche</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Ligne des numéros de jours pour chaque semaine -->
                                {% for semaine in semaines %}
                                <tr style="height: 40px;">
                                    <td class="table-secondary text-center fw-bold">
                                        Semaine {{ forloop.counter }}
                                    </td>
                                    {% for jour in semaine %}
                                    <td class="text-center position-relative {% if jour.is_weekend %}table-light{% endif %} {% if jour.is_today %}table-warning{% endif %}">
                                        {% if jour %}
                                            <small class="fw-bold">{{ jour.day }}</small>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}

                                <!-- Ligne pour chaque stagiaire -->
                                {% for data in stagiaires_data %}
                                <tr style="height: 60px;">
                                    <td class="align-middle text-center" style="background-color: {{ data.color }}20; border-right: 3px solid {{ data.color }};">
                                        <div class="fw-bold text-dark" style="font-size: 0.9rem;">{{ data.stagiaire.nom_complet }}</div>
                                        <small class="text-muted">{{ data.duree_semaines }} sem.</small>
                                        {% if data.stagiaire.sujet %}
                                        <br><small class="text-info" style="font-size: 0.7rem;">{{ data.stagiaire.sujet.titre|truncatechars:20 }}</small>
                                        {% endif %}
                                    </td>

                                    <!-- Créer une barre continue pour la période de stage -->
                                    {% for semaine in semaines %}
                                        {% for jour in semaine %}
                                        <td class="position-relative p-0 {% if jour.is_weekend %}table-light{% endif %}" style="border: 1px solid #dee2e6;">
                                            {% if jour %}
                                                <!-- Vérifier si ce jour fait partie de la période du stagiaire -->
                                                {% for periode in data.periodes %}
                                                    {% if periode.week == forloop.parentloop.counter0 and periode.day == forloop.counter0 %}
                                                        <div class="w-100 h-100 d-flex align-items-center justify-content-center position-relative"
                                                             style="background: linear-gradient(135deg, {{ data.color }}, {{ data.color }}dd);
                                                                    color: white;
                                                                    min-height: 50px;
                                                                    border-radius: 2px;
                                                                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                                            <small class="fw-bold" style="text-shadow: 1px 1px 1px rgba(0,0,0,0.3);">{{ jour.day }}</small>

                                                            <!-- Indicateur de début/fin -->
                                                            {% if data.debut_dans_mois and periode.date == data.stagiaire.date_debut %}
                                                                <span class="position-absolute top-0 start-0 badge bg-success" style="font-size: 0.6rem; border-radius: 0 0 4px 0;">Début</span>
                                                            {% endif %}
                                                            {% if data.fin_dans_mois and periode.date == data.stagiaire.date_fin %}
                                                                <span class="position-absolute top-0 end-0 badge bg-danger" style="font-size: 0.6rem; border-radius: 0 0 0 4px;">Fin</span>
                                                            {% endif %}
                                                        </div>
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Message si aucun stagiaire -->
            {% if not stagiaires_data %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun stage en cours</h4>
                    <p class="text-muted">
                        Aucun stagiaire n'a de stage prévu pour {{ month_name }} {{ year }}.
                    </p>
                    <a href="{% url 'add_stagiaire' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Ajouter un Stagiaire
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Informations supplémentaires -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Statistiques du Mois
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold text-primary fs-4">{{ stagiaires_data|length }}</div>
                                    <small class="text-muted">Stagiaires</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success fs-4">{{ month_name }}</div>
                                    <small class="text-muted">Mois Actuel</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info fs-4">{{ year }}</div>
                                    <small class="text-muted">Année</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Navigation Rapide
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <a href="?year={{ year }}&month=1" class="btn btn-outline-primary btn-sm w-100 mb-2">Janvier</a>
                                    <a href="?year={{ year }}&month=4" class="btn btn-outline-primary btn-sm w-100 mb-2">Avril</a>
                                    <a href="?year={{ year }}&month=7" class="btn btn-outline-primary btn-sm w-100 mb-2">Juillet</a>
                                    <a href="?year={{ year }}&month=10" class="btn btn-outline-primary btn-sm w-100">Octobre</a>
                                </div>
                                <div class="col-6">
                                    <a href="?year={{ year|add:-1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm w-100 mb-2">{{ year|add:-1 }}</a>
                                    <a href="?year={{ year }}&month={{ month }}" class="btn btn-primary btn-sm w-100 mb-2">{{ year }}</a>
                                    <a href="?year={{ year|add:1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm w-100">{{ year|add:1 }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table td, .table th {
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.table-responsive {
    overflow-x: auto;
}

/* Style pour le calendrier */
.table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

/* Animation pour les cellules de stage */
.table td div[style*="background: linear-gradient"] {
    transition: all 0.2s ease;
}

.table td div[style*="background: linear-gradient"]:hover {
    transform: scale(1.05);
    z-index: 10;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* Style pour les weekends */
.table-light {
    background-color: #f8f9fa !important;
    opacity: 0.7;
}

/* Style pour aujourd'hui */
.table-warning {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107 !important;
}

/* Responsive */
@media (max-width: 768px) {
    .table {
        font-size: 0.7rem;
    }

    .table td, .table th {
        padding: 0.2rem;
    }

    .table tbody tr {
        height: 40px !important;
    }

    .fw-bold {
        font-size: 0.7rem !important;
    }

    small {
        font-size: 0.6rem !important;
    }
}

@media (max-width: 576px) {
    .table td:first-child {
        min-width: 100px;
        font-size: 0.6rem;
    }

    .table td div[style*="background: linear-gradient"] {
        min-height: 30px !important;
    }
}

/* Animation d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.5s ease-out;
}

/* Style pour les badges de début/fin */
.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
{% endblock %}
