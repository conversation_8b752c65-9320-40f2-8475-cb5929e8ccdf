{% extends 'stagiaires/base.html' %}

{% block title %}Gestion des Utilisateurs - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Gestion des Utilisateurs (Administrateur)
                    </h3>
                    <a href="{% url 'dashboard' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                    </a>
                </div>
                <div class="card-body">
                    <!-- Avertissement administrateur -->
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Zone Administrateur :</strong> Cette page est exclusivement réservée aux administrateurs du système.
                        Vous avez accès à la gestion complète des comptes utilisateurs.
                    </div>

                    <!-- Actions rapides -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Rechercher un utilisateur...">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{% url 'register' %}" class="btn btn-success">
                                <i class="fas fa-user-plus me-1"></i>Nouvel utilisateur
                            </a>
                            <button class="btn btn-outline-primary ms-2">
                                <i class="fas fa-file-import me-1"></i>Importer
                            </button>
                        </div>
                    </div>

                    <!-- Statistiques utilisateurs -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                   
                            </div>
                        </div>
                    </div>

                    <!-- Liste des utilisateurs -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-user me-1"></i>Utilisateur</th>
                                    <th><i class="fas fa-envelope me-1"></i>Email</th>
                                    <th><i class="fas fa-id-badge me-1"></i>Rôle</th>
                                    <th><i class="fas fa-calendar me-1"></i>Dernière connexion</th>
                                    <th><i class="fas fa-toggle-on me-1"></i>Statut</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_item in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-{% if user_item.role == 'RH' %}warning{% elif user_item.role == 'ENCADRANT' %}info{% else %}secondary{% endif %} text-white me-2">
                                                {{ user_item.first_name|first }}{{ user_item.last_name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ user_item.first_name }} {{ user_item.last_name }}</strong>
                                                <br>
                                                <small class="text-muted">@{{ user_item.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user_item.email }}</td>
                                    <td>
                                        <span class="badge bg-{% if user_item.role == 'RH' %}warning{% elif user_item.role == 'ENCADRANT' %}info{% else %}secondary{% endif %}">
                                            {{ user_item.get_role_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user_item.last_login %}
                                            {{ user_item.last_login|date:"d/m/Y H:i" }}
                                        {% else %}
                                            <span class="text-muted">Jamais connecté</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_item.is_active %}
                                            <span class="badge bg-success">Actif</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="Voir profil" onclick="viewUser('{{ user_item.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="Modifier" onclick="editUser('{{ user_item.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if user_item.is_active %}
                                                <button class="btn btn-outline-secondary" title="Désactiver" onclick="toggleUser('{{ user_item.id }}', false)">
                                                    <i class="fas fa-user-slash"></i>
                                                </button>
                                            {% else %}
                                                <button class="btn btn-outline-success" title="Activer" onclick="toggleUser('{{ user_item.id }}', true)">
                                                    <i class="fas fa-user-check"></i>
                                                </button>
                                            {% endif %}
                                            {% if user_item != user %}
                                                <button class="btn btn-outline-danger" title="Supprimer" onclick="deleteUser('{{ user_item.id }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <br>
                                        Aucun utilisateur trouvé
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">Affichage de {{ users.count }} utilisateur{{ users.count|pluralize }} au total</small>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">Précédent</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link">Suivant</span>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour voir/modifier un utilisateur -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">Détails de l'utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>

<script>
function viewUser(userId) {
    // Simuler l'affichage des détails d'un utilisateur
    document.getElementById('userModalTitle').textContent = 'Détails de l\'utilisateur';
    document.getElementById('userModalBody').innerHTML = `
        <div class="text-center">
            <i class="fas fa-user-circle fa-4x text-primary mb-3"></i>
            <h5>Détails de l'utilisateur #${userId}</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

function editUser(userId) {
    document.getElementById('userModalTitle').textContent = 'Modifier l\'utilisateur';
    document.getElementById('userModalBody').innerHTML = `
        <div class="text-center">
            <i class="fas fa-edit fa-4x text-warning mb-3"></i>
            <h5>Modification de l'utilisateur #${userId}</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

// Fonction pour obtenir le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function showAlert(type, message) {
    // Créer une alerte Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insérer l'alerte en haut de la page
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Supprimer l'alerte après 5 secondes
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function toggleUser(userId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {

        // Préparer les données
        const csrftoken = getCookie('csrftoken');
        const data = { activate: activate };

        // Envoyer la requête AJAX
        fetch(`/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken,
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                // Recharger la page pour mettre à jour l'affichage
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('danger', 'Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('danger', 'Erreur de communication avec le serveur');
        });
    }
}

function deleteUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {

        // Préparer les données
        const csrftoken = getCookie('csrftoken');

        // Envoyer la requête AJAX
        fetch(`/users/${userId}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken,
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                // Recharger la page pour mettre à jour l'affichage
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                // Afficher l'erreur avec plus de détails si disponible
                let errorMessage = data.error;
                if (data.dependencies) {
                    errorMessage += '\n\nDétails des dépendances:';
                    for (const [key, value] of Object.entries(data.dependencies)) {
                        if (value > 0) {
                            errorMessage += `\n- ${key.replace('_', ' ')}: ${value}`;
                        }
                    }
                }
                showAlert('danger', errorMessage.replace(/\n/g, '<br>'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('danger', 'Erreur de communication avec le serveur');
        });
    }
}
</script>
{% endblock %}
