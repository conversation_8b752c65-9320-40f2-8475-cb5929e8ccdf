<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Stagiaires{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            background: #ffffff;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-card {
            background: #ffffff;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        .auth-header {
            background: #ffffff;
            color: #333333;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }
        .auth-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #cccccc;
            box-shadow: 0 0 0 0.2rem rgba(200, 200, 200, 0.25);
        }
        .btn-primary {
            background: #333333;
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            background: #555555;
        }
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid #f0f0f0;
            z-index: 1050;
            position: relative;
        }

        .navbar-logo {
            height: 40px;
            width: auto;
            border-radius: 4px;
            transition: transform 0.3s ease;
        }

        .navbar-logo:hover {
            transform: scale(1.05);
        }

        .navbar-brand {
            font-size: 1.1rem;
            color: #333333;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }

        /* Styles pour les dropdowns personnalisés */
        .dropdown {
            position: relative;
            display: inline-block;
            margin-right: 20px;
        }

        .dropdown-btn {
            background: #ffffff;
            color: #333333;
            border: 1px solid #e9ecef;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .dropdown-btn:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            min-width: 200px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            z-index: 9999;
            padding: 8px 0;
            margin-top: 5px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            list-style: none;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            display: block !important;
            z-index: 9999 !important;
        }

        .dropdown-menu li {
            margin: 0;
            padding: 0;
        }

        .dropdown-menu a {
            display: block;
            padding: 10px 16px;
            color: #333;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 0;
        }

        .dropdown-menu a:hover {
            background: #f8f9fa;
            color: #000000;
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    {% if user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold d-flex align-items-center" href="{% url 'dashboard' %}">
                <img src="https://upload.wikimedia.org/wikipedia/commons/c/c6/Mef-maroc.png"
                     alt="Logo MEF" class="navbar-logo me-2">
                <span>Gestion des Stagiaires</span>
            </a>

            <!-- Bouton toggle pour mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Menus déroulants pour RH -->
                {% if user.role == 'RH' %}
                <ul class="navbar-nav me-auto">
                    <li class="dropdown">
                        <button class="dropdown-btn">Stagiaires ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'stagiaires_list' %}">Stagiaires Existants</a></li>
                            <li><a href="{% url 'conventions_list' %}">Conventions de stage</a></li>
                            <li><a href="{% url 'attestations_list' %}">Attestations de fin de stage</a></li>
                            <li><a href="{% url 'durees_estimees' %}">Durées estimées</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <button class="dropdown-btn">Services ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'services_list' %}">Voir les services</a></li>
                            <li><a href="{% url 'add_service' %}">Ajouter un service</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
                
                <!-- Menus déroulants pour ENCADRANT -->
                {% if user.role == 'ENCADRANT' %}
                <ul class="navbar-nav me-auto">
                    <li class="dropdown">
                        <button class="dropdown-btn">Stagiaires ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'stagiaires_list' %}">Mes Stagiaires</a></li>
                            <li><a href="{% url 'add_stagiaire' %}">Ajouter un stagiaire</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <button class="dropdown-btn">Stage ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'add_thematique' %}">Ajouter une thématique</a></li>
                            <li><a href="{% url 'add_sujet' %}">Ajouter un sujet</a></li>
                            <li><a href="{% url 'durees_estimees' %}">Durées estimées</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <button class="dropdown-btn">Calendrier ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'calendrier_stagiaires' %}">Calendrier des stages</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}

                <!-- Menus déroulants pour ADMIN -->
                {% if user.role == 'ADMIN' %}
                <ul class="navbar-nav me-auto">
                    <li class="dropdown">
                        <button class="dropdown-btn">Administration ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'parametrage' %}">Paramétrage</a></li>
                            <li><a href="{% url 'user_management' %}">Gestion des utilisateurs</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <button class="dropdown-btn">Stagiaires ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'stagiaires_list' %}">Liste des stagiaires</a></li>
                            <li><a href="{% url 'add_stagiaire' %}">Ajouter un stagiaire</a></li>
                            <li><a href="{% url 'conventions_list' %}">Conventions de stage</a></li>
                            <li><a href="{% url 'attestations_list' %}">Attestations de fin de stage</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <button class="dropdown-btn">Thématiques & Sujets ▼</button>
                        <ul class="dropdown-menu">
                            <li><a href="{% url 'thematiques_list' %}">Thématiques</a></li>
                            <li><a href="{% url 'sujets_list' %}">Sujets</a></li>
                            <li><a href="{% url 'add_thematique' %}">Ajouter une thématique</a></li>
                            <li><a href="{% url 'add_sujet' %}">Ajouter un sujet</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}

                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ user.first_name }} {{ user.last_name }}
                        {% if user.role %}
                            <span class="badge bg-primary ms-1">{{ user.get_role_display }}</span>
                        {% endif %}
                    </span>
                    <a class="nav-link" href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt me-1"></i>Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <div class="container-fluid">
        {% if messages %}
            <div class="container mt-3">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // JavaScript pour améliorer l'interaction des dropdowns
        document.addEventListener('DOMContentLoaded', function() {
            const dropdowns = document.querySelectorAll('.dropdown');

            dropdowns.forEach(dropdown => {
                const btn = dropdown.querySelector('.dropdown-btn');
                const menu = dropdown.querySelector('.dropdown-menu');

                // Toggle dropdown au clic
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Fermer tous les autres dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                            otherMenu.classList.remove('show');
                            otherMenu.style.display = 'none';
                        }
                    });

                    // Toggle le dropdown actuel
                    if (menu.classList.contains('show')) {
                        menu.classList.remove('show');
                        menu.style.display = 'none';
                    } else {
                        menu.classList.add('show');
                        menu.style.display = 'block';
                        menu.style.zIndex = '9999';
                    }
                });
            });

            // Fermer les dropdowns en cliquant ailleurs
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    dropdowns.forEach(dropdown => {
                        const menu = dropdown.querySelector('.dropdown-menu');
                        menu.classList.remove('show');
                        menu.style.display = 'none';
                    });
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>




















