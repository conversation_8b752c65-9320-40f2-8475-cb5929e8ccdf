{% extends 'stagiaires/base.html' %}
{% load stagiaires_tags %}

{% block title %}{{ stagiaire.nom_complet }} - Détails du Stagiaire{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <!-- En-tête avec informations principales -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            <i class="fas fa-user me-2"></i>{{ stagiaire.nom_complet }}
                        </h3>
                        <small>{{ stagiaire.get_departement_display }} - {{ stagiaire.specialite }}</small>
                    </div>
                    <div>
                        <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}success{% elif stagiaire.statut == 'TERMINE' %}info{% elif stagiaire.statut == 'SUSPENDU' %}warning{% else %}secondary{% endif %} fs-6">
                            {{ stagiaire.get_statut_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-user me-2"></i>Informations personnelles</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Nom complet :</strong></td><td>{{ stagiaire.nom_complet }}</td></tr>
                                <tr><td><strong>Email :</strong></td><td><a href="mailto:{{ stagiaire.email }}">{{ stagiaire.email }}</a></td></tr>
                                <tr><td><strong>Téléphone :</strong></td><td>{{ stagiaire.telephone|default:"Non renseigné" }}</td></tr>
                                <tr><td><strong>Date de naissance :</strong></td><td>{{ stagiaire.date_naissance|date:"d/m/Y"|default:"Non renseignée" }}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info"><i class="fas fa-calendar me-2"></i>Période de stage</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Date de début :</strong></td><td>{{ stagiaire.date_debut|date:"d/m/Y" }}</td></tr>
                                <tr><td><strong>Date de fin :</strong></td><td>{{ stagiaire.date_fin|date:"d/m/Y" }}</td></tr>
                                <tr><td><strong>Durée :</strong></td><td>{{ stagiaire.duree_stage }} jours</td></tr>
                                <tr><td><strong>Encadrant :</strong></td><td>{{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Informations académiques -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Informations académiques</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Établissement :</strong> {{ stagiaire.etablissement|default:"Non renseigné" }}</p>
                            <p><strong>Niveau d'étude :</strong> {{ stagiaire.niveau_etude|default:"Non renseigné" }}</p>
                            <p><strong>Spécialité :</strong> {{ stagiaire.specialite|default:"Non renseignée" }}</p>
                            <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                            {% if stagiaire.service %}
                            <p><strong>Service :</strong> {{ stagiaire.service.nom }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Informations de stage -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Informations de stage</h5>
                        </div>
                        <div class="card-body">
                            {% if stagiaire.thematique %}
                            <p><strong>Thématique :</strong> {{ stagiaire.thematique.nom }}</p>
                            {% endif %}
                            {% if stagiaire.sujet %}
                            <p><strong>Sujet :</strong> {{ stagiaire.sujet.titre }}</p>
                            {% endif %}
                            {% if stagiaire.description_taches %}
                            <p><strong>Description des tâches :</strong></p>
                            <div class="text-muted">{{ stagiaire.description_taches|linebreaks }}</div>
                            {% endif %}
                            <p><strong>Créé le :</strong> {{ stagiaire.date_creation|date:"d/m/Y à H:i" }}</p>
                            <p><strong>Créé par :</strong> {{ stagiaire.cree_par.get_full_name }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technologies utilisées -->
            {% if stagiaire.technologies %}
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-laptop-code me-2"></i>Technologies utilisées</h5>
                </div>
                <div class="card-body">
                    <div class="tech-tags">
                        {% for tech in stagiaire.technologies|split:',' %}
                            <span class="badge bg-light text-dark p-2 me-2 mb-2">{{ tech|trim }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Documents -->
            {% if user.role == 'RH' or user.role == 'ADMIN' %}
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Documents</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-pdf fa-2x text-primary mb-2"></i>
                                    <h6>CV</h6>
                                    {% if stagiaire.cv %}
                                        <a href="{{ stagiaire.cv.url }}" target="_blank" class="btn btn-primary btn-sm">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Non fourni</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-contract fa-2x text-success mb-2"></i>
                                    <h6>Convention de stage</h6>
                                    {% if stagiaire.convention_stage %}
                                        <a href="{{ stagiaire.convention_stage.url }}" target="_blank" class="btn btn-success btn-sm">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                        <div class="mt-2">
                                            <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                                {{ stagiaire.get_statut_convention_display|default:"En attente" }}
                                            </span>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Non fournie</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                                    <h6>Assurance</h6>
                                    {% if stagiaire.assurance %}
                                        <a href="{{ stagiaire.assurance.url }}" target="_blank" class="btn btn-warning btn-sm">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Non fournie</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Progression et statut -->
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Progression du stage</h5>
                </div>
                <div class="card-body">
                    {% with info=stagiaire.get_progress_info %}
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Avancement temporel</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-primary"
                                     style="width: {{ info.progress_percentage }}%"
                                     role="progressbar"
                                     aria-valuenow="{{ info.progress_percentage }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                    {{ info.progress_percentage }}%
                                </div>
                            </div>
                            <p class="text-muted">
                                {{ info.days_elapsed }} jours écoulés sur {{ info.total_days }} jours au total
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Statut des tâches</h6>
                            <span class="badge bg-{% if stagiaire.statut_taches == 'ACCOMPLIES' %}success{% elif stagiaire.statut_taches == 'EN_COURS' %}warning{% elif stagiaire.statut_taches == 'PARTIELLEMENT_ACCOMPLIES' %}info{% else %}secondary{% endif %} fs-6">
                                {{ stagiaire.get_statut_taches_display|default:"Non commencées" }}
                            </span>
                            {% if stagiaire.evaluation_encadrant %}
                            <div class="mt-3">
                                <h6>Évaluation de l'encadrant</h6>
                                <div class="text-muted">{{ stagiaire.evaluation_encadrant|linebreaks }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endwith %}
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                            </a>
                        </div>
                        <div>
                            {% if user.role == 'ADMIN' or user.role == 'RH' %}
                            <a href="{% url 'edit_stagiaire' stagiaire.id %}" class="btn btn-primary me-2">
                                <i class="fas fa-edit me-1"></i>Modifier
                            </a>
                            {% endif %}

                            {% if user.role == 'ENCADRANT' and stagiaire.encadrant == user %}
                            <button class="btn btn-info me-2" onclick="alert('Fonctionnalité missions à venir')">
                                <i class="fas fa-tasks me-1"></i>Missions
                            </button>
                            <button class="btn btn-warning me-2" onclick="alert('Fonctionnalité tâches à venir')">
                                <i class="fas fa-list-check me-1"></i>Tâches
                            </button>
                            {% endif %}

                            {% if user.role == 'RH' and stagiaire.convention_stage and stagiaire.statut_convention == 'VALIDEE' %}
                            <button class="btn btn-success" onclick="alert('Fonctionnalité contrat à venir')">
                                <i class="fas fa-file-contract me-1"></i>Créer contrat
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.tech-tags .badge {
    font-size: 0.9em;
    transition: all 0.2s;
}

.tech-tags .badge:hover {
    transform: scale(1.05);
}

.card {
    transition: all 0.2s;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}